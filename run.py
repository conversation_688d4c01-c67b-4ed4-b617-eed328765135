#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script untuk menjalankan Generator Prompt Signal Binance
Dengan konfigurasi yang mudah dan user-friendly
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Cek versi Python"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ diperlukan!")
        print(f"Versi saat ini: {sys.version}")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} - OK")

def install_requirements():
    """Install dependencies jika belum ada"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ File requirements.txt tidak ditemukan!")
        return False
    
    try:
        print("📦 Menginstall dependencies...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies berhasil diinstall")
        return True
    except subprocess.CalledProcessError:
        print("❌ Gagal menginstall dependencies")
        return False

def setup_gemini_api():
    """Setup Gemini API key"""
    api_key = os.getenv('GEMINI_API_KEY')
    
    if not api_key or api_key == 'YOUR_GEMINI_API_KEY_HERE':
        print("\n🔑 Setup Gemini API Key")
        print("Untuk mendapatkan API key:")
        print("1. Kunjungi: https://makersuite.google.com/app/apikey")
        print("2. Buat API key baru")
        print("3. Copy API key")
        
        user_key = input("\nMasukkan Gemini API Key (atau tekan Enter untuk skip): ").strip()
        
        if user_key:
            os.environ['GEMINI_API_KEY'] = user_key
            print("✅ API key berhasil diset untuk sesi ini")
            
            # Simpan ke file .env untuk penggunaan selanjutnya
            with open('.env', 'w') as f:
                f.write(f'GEMINI_API_KEY={user_key}\n')
            print("💾 API key disimpan ke file .env")
        else:
            print("⚠️  Aplikasi akan berjalan tanpa AI analysis")
    else:
        print("✅ Gemini API key sudah dikonfigurasi")

def load_env_file():
    """Load environment variables dari file .env"""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded dari .env")

def main():
    """Fungsi utama"""
    print("🚀 Generator Prompt Signal Binance - Setup")
    print("=" * 50)
    
    # Cek versi Python
    check_python_version()
    
    # Load environment variables
    load_env_file()
    
    # Install dependencies
    if not install_requirements():
        print("❌ Setup gagal - tidak bisa install dependencies")
        sys.exit(1)
    
    # Setup API key
    setup_gemini_api()
    
    print("\n" + "=" * 50)
    print("🎯 Setup selesai! Memulai aplikasi...")
    print("🌐 Aplikasi akan tersedia di: http://localhost:5000")
    print("⚠️  Tekan Ctrl+C untuk menghentikan aplikasi")
    print("=" * 50)
    
    # Import dan jalankan aplikasi
    try:
        from generator_prompt_signal_binance import main as run_app
        run_app()
    except KeyboardInterrupt:
        print("\n👋 Aplikasi dihentikan oleh user")
    except ImportError as e:
        print(f"❌ Error import: {e}")
        print("Pastikan file generator_prompt_signal_binance.py ada di direktori yang sama")
    except Exception as e:
        print(f"❌ Error menjalankan aplikasi: {e}")

if __name__ == "__main__":
    main()
