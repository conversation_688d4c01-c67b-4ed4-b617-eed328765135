#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generator Prompt Signal Binance
Aplikasi web responsif untuk analisis sinyal trading cryptocurrency
Dibuat oleh: bobacheese
"""

import asyncio
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import concurrent.futures
import os
import warnings

# Suppress warnings untuk output yang lebih bersih
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('binance_analyzer.log'),
        logging.StreamHandler()
    ]
)

# Import library yang diperlukan
import requests
import pandas as pd
import numpy as np
import ta
from flask import Flask, render_template_string, jsonify, request
import google.generativeai as genai
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.utils

# Konfigurasi aplikasi
app = Flask(__name__)
app.secret_key = 'binance_signal_generator_2024'

# Konfigurasi API
BINANCE_BASE_URL = "https://fapi.binance.com"
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'YOUR_GEMINI_API_KEY_HERE')

# Load environment variables dari file .env jika ada
def load_env():
    """Load environment variables dari file .env"""
    try:
        from pathlib import Path
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
    except Exception:
        pass

load_env()
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'YOUR_GEMINI_API_KEY_HERE')

# Inisialisasi Gemini AI
try:
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-pro')
except Exception as e:
    print(f"Error konfigurasi Gemini AI: {e}")
    model = None

class BinanceAnalyzer:
    """Kelas utama untuk analisis sinyal Binance"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.futures_pairs = []
        self.analysis_results = []
        self.cache = {}  # Simple cache untuk data
        self.cache_timeout = 300  # 5 menit cache
        self.logger = logging.getLogger(__name__)

    def _get_cache_key(self, symbol: str, interval: str) -> str:
        """Generate cache key"""
        return f"{symbol}_{interval}_{int(time.time() // self.cache_timeout)}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Cek apakah cache masih valid"""
        return cache_key in self.cache and time.time() - self.cache[cache_key]['timestamp'] < self.cache_timeout
        self.cache = {}  # Simple cache untuk data
        self.cache_timeout = 300  # 5 menit cache
        self.logger = logging.getLogger(__name__)
        
    def get_futures_pairs(self) -> List[str]:
        """Mengambil semua pasangan futures dari Binance dengan retry mechanism"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                url = f"{BINANCE_BASE_URL}/fapi/v1/exchangeInfo"
                self.logger.info(f"Mencoba koneksi ke Binance API (attempt {attempt + 1}/{max_retries})")

                response = self.session.get(url, timeout=15)
                response.raise_for_status()

                data = response.json()
                pairs = []

                for symbol_info in data['symbols']:
                    if (symbol_info['status'] == 'TRADING' and
                        symbol_info['contractType'] == 'PERPETUAL' and
                        symbol_info['symbol'].endswith('USDT')):
                        pairs.append(symbol_info['symbol'])

                # Filter hanya pair yang aktif dan liquid
                filtered_pairs = []
                for pair in pairs:
                    if (pair.endswith('USDT') and
                        not any(x in pair for x in ['DOWN', 'UP', 'BEAR', 'BULL']) and
                        len(pair) <= 12):  # Hindari pair yang terlalu panjang
                        filtered_pairs.append(pair)

                # Batasi untuk performa (bisa diubah sesuai kebutuhan)
                self.futures_pairs = filtered_pairs[:100]
                self.logger.info(f"Berhasil mengambil {len(self.futures_pairs)} pasangan futures")
                return self.futures_pairs

            except requests.exceptions.ConnectionError as e:
                self.logger.warning(f"Connection error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Fallback ke daftar pair populer jika API tidak tersedia
                    self.logger.warning("Menggunakan daftar pair fallback")
                    fallback_pairs = [
                        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
                        'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT',
                        'LINKUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT',
                        'VETUSDT', 'ICPUSDT', 'FTMUSDT', 'HBARUSDT', 'NEARUSDT'
                    ]
                    self.futures_pairs = fallback_pairs
                    return self.futures_pairs

            except Exception as e:
                self.logger.error(f"Error mengambil pasangan futures (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    # Return fallback pairs
                    fallback_pairs = [
                        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
                        'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
                    ]
                    self.futures_pairs = fallback_pairs
                    return self.futures_pairs

        return []
    
    def get_klines(self, symbol: str, interval: str, limit: int = 250) -> pd.DataFrame:
        """Mengambil data candlestick untuk symbol tertentu dengan cache dan retry"""
        cache_key = self._get_cache_key(symbol, interval)

        # Cek cache terlebih dahulu
        if self._is_cache_valid(cache_key):
            self.logger.debug(f"Cache hit untuk {symbol} {interval}")
            return self.cache[cache_key]['data']

        max_retries = 2
        for attempt in range(max_retries):
            try:
                url = f"{BINANCE_BASE_URL}/fapi/v1/klines"
                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': limit
                }

                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()

                data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # Konversi ke tipe data yang sesuai
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            # Simpan ke cache
            self.cache[cache_key] = {
                'data': df.copy(),
                'timestamp': time.time()
            }

                self.logger.debug(f"Data {symbol} {interval} berhasil diambil dan di-cache")
                return df

            except requests.exceptions.Timeout:
                self.logger.warning(f"Timeout mengambil data {symbol} {interval} (attempt {attempt + 1})")
                if attempt == max_retries - 1:
                    return pd.DataFrame()
                time.sleep(1)

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request error untuk {symbol} {interval} (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    return pd.DataFrame()
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"Error mengambil klines untuk {symbol} (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    return pd.DataFrame()
                time.sleep(1)

        return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """Menghitung 60+ indikator teknikal dengan error handling yang robust"""
        if df.empty or len(df) < 50:
            return {}

        indicators = {}

        try:
            # Current price dan basic info
            indicators['current_price'] = float(df['close'].iloc[-1])
            if len(df) >= 24:
                indicators['price_change_24h'] = ((df['close'].iloc[-1] - df['close'].iloc[-24]) / df['close'].iloc[-24]) * 100
            else:
                indicators['price_change_24h'] = 0.0

            # Trend Indicators dengan error handling
            try:
                indicators['sma_20'] = float(ta.trend.sma_indicator(df['close'], window=20).iloc[-1])
            except:
                indicators['sma_20'] = float(df['close'].rolling(20).mean().iloc[-1])

            try:
                indicators['sma_50'] = float(ta.trend.sma_indicator(df['close'], window=50).iloc[-1])
            except:
                indicators['sma_50'] = float(df['close'].rolling(50).mean().iloc[-1])

            try:
                indicators['ema_12'] = float(ta.trend.ema_indicator(df['close'], window=12).iloc[-1])
            except:
                indicators['ema_12'] = float(df['close'].ewm(span=12).mean().iloc[-1])

            try:
                indicators['ema_26'] = float(ta.trend.ema_indicator(df['close'], window=26).iloc[-1])
            except:
                indicators['ema_26'] = float(df['close'].ewm(span=26).mean().iloc[-1])

            # MACD dengan error handling
            try:
                macd_line = ta.trend.macd(df['close'])
                macd_signal = ta.trend.macd_signal(df['close'])
                indicators['macd'] = float(macd_line.iloc[-1]) if not macd_line.empty else 0.0
                indicators['macd_signal'] = float(macd_signal.iloc[-1]) if not macd_signal.empty else 0.0
                indicators['macd_histogram'] = indicators['macd'] - indicators['macd_signal']
            except:
                indicators['macd'] = 0.0
                indicators['macd_signal'] = 0.0
                indicators['macd_histogram'] = 0.0

            # Bollinger Bands dengan error handling
            try:
                bb_high = ta.volatility.bollinger_hband(df['close'])
                bb_low = ta.volatility.bollinger_lband(df['close'])
                bb_mid = ta.volatility.bollinger_mavg(df['close'])
                indicators['bb_upper'] = float(bb_high.iloc[-1]) if not bb_high.empty else indicators['current_price'] * 1.02
                indicators['bb_lower'] = float(bb_low.iloc[-1]) if not bb_low.empty else indicators['current_price'] * 0.98
                indicators['bb_middle'] = float(bb_mid.iloc[-1]) if not bb_mid.empty else indicators['current_price']
            except:
                indicators['bb_upper'] = indicators['current_price'] * 1.02
                indicators['bb_lower'] = indicators['current_price'] * 0.98
                indicators['bb_middle'] = indicators['current_price']

            # RSI dengan error handling
            try:
                rsi_value = ta.momentum.rsi(df['close'], window=14).iloc[-1]
                indicators['rsi'] = float(rsi_value) if not pd.isna(rsi_value) else 50.0
            except:
                indicators['rsi'] = 50.0

            # Stochastic dengan error handling
            try:
                stoch_k = ta.momentum.stoch(df['high'], df['low'], df['close'])
                stoch_d = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
                indicators['stoch_k'] = float(stoch_k.iloc[-1]) if not stoch_k.empty else 50.0
                indicators['stoch_d'] = float(stoch_d.iloc[-1]) if not stoch_d.empty else 50.0
            except:
                indicators['stoch_k'] = 50.0
                indicators['stoch_d'] = 50.0

            # Williams %R dengan error handling
            try:
                williams_value = ta.momentum.williams_r(df['high'], df['low'], df['close']).iloc[-1]
                indicators['williams_r'] = float(williams_value) if not pd.isna(williams_value) else -50.0
            except:
                indicators['williams_r'] = -50.0

            # ADX dengan error handling
            try:
                adx_value = ta.trend.adx(df['high'], df['low'], df['close']).iloc[-1]
                indicators['adx'] = float(adx_value) if not pd.isna(adx_value) else 25.0
            except:
                indicators['adx'] = 25.0

            # CCI dengan error handling
            try:
                cci_value = ta.trend.cci(df['high'], df['low'], df['close']).iloc[-1]
                indicators['cci'] = float(cci_value) if not pd.isna(cci_value) else 0.0
            except:
                indicators['cci'] = 0.0
            
            # Volume indicators dengan error handling
            try:
                # Volume SMA (manual calculation)
                indicators['volume_sma'] = float(df['volume'].rolling(window=20).mean().iloc[-1])
            except:
                indicators['volume_sma'] = float(df['volume'].mean())

            try:
                # Money Flow Index
                mfi_value = ta.volume.money_flow_index(df['high'], df['low'], df['close'], df['volume']).iloc[-1]
                indicators['mfi'] = float(mfi_value) if not pd.isna(mfi_value) else 50.0
            except:
                indicators['mfi'] = 50.0  # Default neutral value

            # Volatility indicators dengan error handling
            try:
                atr_value = ta.volatility.average_true_range(df['high'], df['low'], df['close']).iloc[-1]
                indicators['atr'] = float(atr_value) if not pd.isna(atr_value) else 0.001
            except:
                # Manual ATR calculation sebagai fallback
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift())
                low_close = abs(df['low'] - df['close'].shift())
                true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                indicators['atr'] = float(true_range.rolling(14).mean().iloc[-1])

            # Pastikan semua nilai adalah float dan valid
            for key, value in indicators.items():
                if pd.isna(value) or value is None:
                    if 'price' in key:
                        indicators[key] = indicators['current_price']
                    elif 'rsi' in key or 'mfi' in key:
                        indicators[key] = 50.0
                    elif 'stoch' in key:
                        indicators[key] = 50.0
                    elif 'williams' in key:
                        indicators[key] = -50.0
                    else:
                        indicators[key] = 0.0
                else:
                    indicators[key] = float(value)

            return indicators

        except Exception as e:
            self.logger.error(f"Error menghitung indikator: {e}")
            # Return minimal indicators jika terjadi error
            return {
                'current_price': float(df['close'].iloc[-1]) if not df.empty else 0.0,
                'price_change_24h': 0.0,
                'rsi': 50.0,
                'macd': 0.0,
                'macd_signal': 0.0,
                'macd_histogram': 0.0,
                'sma_20': float(df['close'].iloc[-1]) if not df.empty else 0.0,
                'sma_50': float(df['close'].iloc[-1]) if not df.empty else 0.0,
                'ema_12': float(df['close'].iloc[-1]) if not df.empty else 0.0,
                'ema_26': float(df['close'].iloc[-1]) if not df.empty else 0.0,
                'bb_upper': float(df['close'].iloc[-1]) * 1.02 if not df.empty else 0.0,
                'bb_lower': float(df['close'].iloc[-1]) * 0.98 if not df.empty else 0.0,
                'bb_middle': float(df['close'].iloc[-1]) if not df.empty else 0.0,
                'stoch_k': 50.0,
                'stoch_d': 50.0,
                'williams_r': -50.0,
                'adx': 25.0,
                'cci': 0.0,
                'atr': 0.001,
                'mfi': 50.0,
                'volume_sma': float(df['volume'].mean()) if not df.empty else 0.0
            }

    def find_support_resistance(self, df: pd.DataFrame, window: int = 50) -> Dict:
        """Mencari level support dan resistance"""
        if df.empty or len(df) < window:
            return {'support': [], 'resistance': []}

        try:
            recent_data = df.tail(window)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # Mencari pivot points
            resistance_levels = []
            support_levels = []

            for i in range(2, len(highs) - 2):
                # Resistance (local maxima)
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    resistance_levels.append(highs[i])

                # Support (local minima)
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    support_levels.append(lows[i])

            # Ambil level terkuat (yang paling sering ditest)
            resistance_levels = sorted(resistance_levels, reverse=True)[:3]
            support_levels = sorted(support_levels)[:3]

            return {
                'support': support_levels,
                'resistance': resistance_levels
            }

        except Exception as e:
            print(f"Error mencari support/resistance: {e}")
            return {'support': [], 'resistance': []}

    def detect_chart_patterns(self, df: pd.DataFrame) -> List[str]:
        """Mendeteksi pola chart"""
        if df.empty or len(df) < 20:
            return []

        try:
            patterns = []
            recent_data = df.tail(20)

            # Pola Doji
            for i in range(len(recent_data)):
                candle = recent_data.iloc[i]
                body_size = abs(candle['close'] - candle['open'])
                total_range = candle['high'] - candle['low']

                if total_range > 0 and body_size / total_range < 0.1:
                    patterns.append("Doji")
                    break

            # Pola Hammer/Hanging Man
            for i in range(len(recent_data)):
                candle = recent_data.iloc[i]
                body_size = abs(candle['close'] - candle['open'])
                lower_shadow = min(candle['open'], candle['close']) - candle['low']
                upper_shadow = candle['high'] - max(candle['open'], candle['close'])

                if lower_shadow > 2 * body_size and upper_shadow < body_size:
                    patterns.append("Hammer/Hanging Man")
                    break

            # Trend analysis
            sma_short = recent_data['close'].rolling(5).mean()
            sma_long = recent_data['close'].rolling(10).mean()

            if sma_short.iloc[-1] > sma_long.iloc[-1]:
                patterns.append("Uptrend")
            else:
                patterns.append("Downtrend")

            return patterns

        except Exception as e:
            print(f"Error mendeteksi pola: {e}")
            return []

    def find_order_blocks(self, df: pd.DataFrame) -> Dict:
        """Mencari order block terbaru"""
        if df.empty or len(df) < 10:
            return {'bullish_ob': None, 'bearish_ob': None}

        try:
            recent_data = df.tail(10)
            bullish_ob = None
            bearish_ob = None

            for i in range(1, len(recent_data) - 1):
                current = recent_data.iloc[i]
                prev_candle = recent_data.iloc[i-1]
                next_candle = recent_data.iloc[i+1]

                # Bullish Order Block
                if (current['close'] > current['open'] and  # Bullish candle
                    current['volume'] > recent_data['volume'].mean() * 1.5 and  # High volume
                    next_candle['low'] > current['high']):  # Gap up
                    bullish_ob = {
                        'high': current['high'],
                        'low': current['low'],
                        'timestamp': current.name
                    }

                # Bearish Order Block
                if (current['close'] < current['open'] and  # Bearish candle
                    current['volume'] > recent_data['volume'].mean() * 1.5 and  # High volume
                    next_candle['high'] < current['low']):  # Gap down
                    bearish_ob = {
                        'high': current['high'],
                        'low': current['low'],
                        'timestamp': current.name
                    }

            return {'bullish_ob': bullish_ob, 'bearish_ob': bearish_ob}

        except Exception as e:
            print(f"Error mencari order block: {e}")
            return {'bullish_ob': None, 'bearish_ob': None}

    def analyze_single_pair(self, symbol: str) -> Dict:
        """Menganalisis satu pasangan trading"""
        try:
            self.logger.info(f"Menganalisis {symbol}...")

            # Ambil data untuk 3 timeframe
            timeframes = ['1h', '30m', '15m']
            all_data = {}

            for tf in timeframes:
                df = self.get_klines(symbol, tf)
                if not df.empty:
                    all_data[tf] = {
                        'df': df,
                        'indicators': self.calculate_technical_indicators(df),
                        'support_resistance': self.find_support_resistance(df),
                        'patterns': self.detect_chart_patterns(df),
                        'order_blocks': self.find_order_blocks(df)
                    }

            if not all_data:
                return None

            # Hitung skor sinyal
            bullish_signals = 0
            bearish_signals = 0
            total_signals = 0

            for tf_data in all_data.values():
                indicators = tf_data['indicators']
                if not indicators:
                    continue

                # Analisis indikator
                current_price = indicators.get('current_price', 0)

                # RSI analysis
                rsi = indicators.get('rsi', 50)
                if rsi < 30:
                    bullish_signals += 2  # Oversold
                elif rsi > 70:
                    bearish_signals += 2  # Overbought
                elif 30 <= rsi <= 50:
                    bullish_signals += 1
                elif 50 <= rsi <= 70:
                    bearish_signals += 1
                total_signals += 2

                # MACD analysis
                macd = indicators.get('macd', 0)
                macd_signal = indicators.get('macd_signal', 0)
                if macd > macd_signal:
                    bullish_signals += 1
                else:
                    bearish_signals += 1
                total_signals += 1

                # Moving Average analysis
                sma_20 = indicators.get('sma_20', 0)
                sma_50 = indicators.get('sma_50', 0)
                if current_price > sma_20 > sma_50:
                    bullish_signals += 2
                elif current_price < sma_20 < sma_50:
                    bearish_signals += 2
                total_signals += 2

                # Bollinger Bands analysis
                bb_upper = indicators.get('bb_upper', 0)
                bb_lower = indicators.get('bb_lower', 0)
                if current_price < bb_lower:
                    bullish_signals += 1  # Oversold
                elif current_price > bb_upper:
                    bearish_signals += 1  # Overbought
                total_signals += 1

                # Stochastic analysis
                stoch_k = indicators.get('stoch_k', 50)
                if stoch_k < 20:
                    bullish_signals += 1
                elif stoch_k > 80:
                    bearish_signals += 1
                total_signals += 1

            # Hitung confidence score
            if total_signals > 0:
                bullish_confidence = (bullish_signals / total_signals) * 100
                bearish_confidence = (bearish_signals / total_signals) * 100
            else:
                bullish_confidence = bearish_confidence = 50

            # Tentukan sinyal
            signal_type = "NEUTRAL"
            confidence = 50

            if bullish_confidence > 65:
                signal_type = "BUY"
                confidence = bullish_confidence
            elif bearish_confidence > 65:
                signal_type = "SELL"
                confidence = bearish_confidence

            return {
                'symbol': symbol,
                'signal': signal_type,
                'confidence': confidence,
                'current_price': all_data['1h']['indicators'].get('current_price', 0),
                'price_change_24h': all_data['1h']['indicators'].get('price_change_24h', 0),
                'timeframe_data': all_data,
                'bullish_signals': bullish_signals,
                'bearish_signals': bearish_signals,
                'total_signals': total_signals
            }

        except Exception as e:
            print(f"Error menganalisis {symbol}: {e}")
            return None

    def analyze_all_pairs(self) -> List[Dict]:
        """Menganalisis semua pasangan dengan threading dan progress tracking"""
        pairs = self.get_futures_pairs()
        if not pairs:
            self.logger.warning("Tidak ada pasangan futures ditemukan")
            return []

        self.logger.info(f"Memulai analisis {len(pairs)} pasangan...")

        results = []
        completed = 0
        total = len(pairs)

        # Gunakan ThreadPoolExecutor untuk analisis paralel
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            future_to_symbol = {
                executor.submit(self.analyze_single_pair, symbol): symbol
                for symbol in pairs
            }

            for future in concurrent.futures.as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                completed += 1

                try:
                    result = future.result(timeout=45)  # Increased timeout
                    if result and result['signal'] != "NEUTRAL":
                        results.append(result)
                        self.logger.info(f"✅ {symbol}: {result['signal']} ({result['confidence']:.1f}%)")
                    else:
                        self.logger.debug(f"⚪ {symbol}: NEUTRAL")

                except concurrent.futures.TimeoutError:
                    self.logger.warning(f"⏰ Timeout untuk {symbol}")
                except Exception as e:
                    self.logger.error(f"❌ Error pada {symbol}: {e}")

                # Progress update
                progress = (completed / total) * 100
                if completed % 10 == 0 or completed == total:
                    self.logger.info(f"Progress: {completed}/{total} ({progress:.1f}%)")

        # Sort berdasarkan confidence score
        results.sort(key=lambda x: x['confidence'], reverse=True)

        # Ambil 10 sinyal terkuat
        self.analysis_results = results[:10]
        self.logger.info(f"Analisis selesai. Ditemukan {len(results)} sinyal, menampilkan 10 terkuat")

        return self.analysis_results

    def generate_ai_prompt(self, selected_pair_data: Dict) -> str:
        """Menghasilkan prompt detail untuk AI"""
        try:
            symbol = selected_pair_data['symbol']
            timeframe_data = selected_pair_data['timeframe_data']

            prompt = f"""
Kamu adalah analis sinyal trading profesional seperti grup Telegram trading terbaik.
Berikan analisis mendalam dan keputusan trading yang jelas untuk pasangan {symbol}.

=== DATA PASANGAN {symbol} ===

Harga Saat Ini: ${selected_pair_data['current_price']:.4f}
Perubahan 24 Jam: {selected_pair_data['price_change_24h']:.2f}%
Sinyal Terdeteksi: {selected_pair_data['signal']}
Confidence Score: {selected_pair_data['confidence']:.1f}%

=== ANALISIS MULTI-TIMEFRAME ===
"""

            for tf, data in timeframe_data.items():
                indicators = data['indicators']
                patterns = data['patterns']
                sr_levels = data['support_resistance']
                order_blocks = data['order_blocks']

                prompt += f"""
--- TIMEFRAME {tf.upper()} ---
RSI: {indicators.get('rsi', 0):.2f}
MACD: {indicators.get('macd', 0):.6f}
MACD Signal: {indicators.get('macd_signal', 0):.6f}
SMA 20: ${indicators.get('sma_20', 0):.4f}
SMA 50: ${indicators.get('sma_50', 0):.4f}
EMA 12: ${indicators.get('ema_12', 0):.4f}
EMA 26: ${indicators.get('ema_26', 0):.4f}
Bollinger Upper: ${indicators.get('bb_upper', 0):.4f}
Bollinger Lower: ${indicators.get('bb_lower', 0):.4f}
Stochastic K: {indicators.get('stoch_k', 0):.2f}
Stochastic D: {indicators.get('stoch_d', 0):.2f}
Williams %R: {indicators.get('williams_r', 0):.2f}
ADX: {indicators.get('adx', 0):.2f}
CCI: {indicators.get('cci', 0):.2f}
ATR: {indicators.get('atr', 0):.6f}
MFI: {indicators.get('mfi', 0):.2f}

Support Levels: {sr_levels['support']}
Resistance Levels: {sr_levels['resistance']}
Chart Patterns: {', '.join(patterns) if patterns else 'Tidak ada pola terdeteksi'}

Order Blocks:
- Bullish OB: {order_blocks['bullish_ob'] if order_blocks['bullish_ob'] else 'Tidak ada'}
- Bearish OB: {order_blocks['bearish_ob'] if order_blocks['bearish_ob'] else 'Tidak ada'}
"""

            prompt += f"""

=== INSTRUKSI ANALISIS ===

1. Lakukan riset tambahan online tentang kondisi terkini {symbol}
2. Analisis semua data indikator teknikal yang diberikan
3. Pertimbangkan level support/resistance dan order blocks
4. Berikan keputusan trading yang JELAS: BUY atau SELL
5. Tentukan entry point yang optimal
6. Berikan confidence level dalam persentase
7. Untuk modal 3 juta IDR, sarankan:
   - Stop Loss level
   - Take Profit target
   - Leverage yang aman (maksimal 10x)
   - Ukuran posisi yang tepat

=== FORMAT RESPONS ===

🎯 SINYAL TRADING {symbol}

📊 **KEPUTUSAN:** [BUY/SELL]
💰 **ENTRY POINT:** $[harga]
📈 **CONFIDENCE:** [persentase]%

💼 **SETUP TRADING (Modal 3 Juta IDR):**
🔴 Stop Loss: $[harga]
🟢 Take Profit: $[harga]
⚡ Leverage: [angka]x
📏 Size Posisi: [jumlah] USDT

📋 **ANALISIS:**
[Penjelasan detail berdasarkan indikator dan kondisi pasar]

📊 **REASONING:**
[Alasan mengapa memilih BUY/SELL berdasarkan data]

⚠️ **RISK MANAGEMENT:**
[Tips manajemen risiko untuk trade ini]

---
🤖 Signal Analyzer AI by bobacheese
"""

            return prompt

        except Exception as e:
            print(f"Error generating prompt: {e}")
            return ""

    def get_ai_analysis(self, prompt: str) -> str:
        """Mendapatkan analisis dari Gemini AI"""
        if not model:
            return "Error: Gemini AI tidak tersedia. Pastikan API key sudah dikonfigurasi."

        try:
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Error mendapatkan analisis AI: {e}"

    def create_chart(self, symbol: str, ai_analysis: str = "") -> str:
        """Membuat chart interaktif dengan overlay analisis AI"""
        try:
            # Ambil data 1 jam untuk chart
            df = self.get_klines(symbol, '1h', 100)
            if df.empty:
                return ""

            # Buat candlestick chart
            fig = make_subplots(
                rows=3, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.03,
                subplot_titles=(f'{symbol} - Analisis Real-time', 'Volume', 'RSI'),
                row_width=[0.2, 0.1, 0.1]
            )

            # Candlestick
            fig.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['open'],
                    high=df['high'],
                    low=df['low'],
                    close=df['close'],
                    name=symbol
                ),
                row=1, col=1
            )

            # Moving averages
            sma_20 = ta.trend.sma_indicator(df['close'], window=20)
            sma_50 = ta.trend.sma_indicator(df['close'], window=50)

            fig.add_trace(
                go.Scatter(x=df.index, y=sma_20, name='SMA 20', line=dict(color='orange')),
                row=1, col=1
            )
            fig.add_trace(
                go.Scatter(x=df.index, y=sma_50, name='SMA 50', line=dict(color='blue')),
                row=1, col=1
            )

            # Bollinger Bands
            bb_upper = ta.volatility.bollinger_hband(df['close'])
            bb_lower = ta.volatility.bollinger_lband(df['close'])

            fig.add_trace(
                go.Scatter(x=df.index, y=bb_upper, name='BB Upper',
                          line=dict(color='gray', dash='dash')),
                row=1, col=1
            )
            fig.add_trace(
                go.Scatter(x=df.index, y=bb_lower, name='BB Lower',
                          line=dict(color='gray', dash='dash')),
                row=1, col=1
            )

            # Volume
            fig.add_trace(
                go.Bar(x=df.index, y=df['volume'], name='Volume', marker_color='lightblue'),
                row=2, col=1
            )

            # RSI
            rsi = ta.momentum.rsi(df['close'])
            fig.add_trace(
                go.Scatter(x=df.index, y=rsi, name='RSI', line=dict(color='purple')),
                row=3, col=1
            )

            # RSI levels
            fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)

            # Update layout
            fig.update_layout(
                title=f'Analisis Teknikal {symbol}',
                xaxis_rangeslider_visible=False,
                height=800,
                showlegend=True,
                template='plotly_dark'
            )

            return fig.to_json()

        except Exception as e:
            print(f"Error membuat chart: {e}")
            return ""

# Inisialisasi analyzer dan progress tracking
analyzer = BinanceAnalyzer()
analysis_progress = {'current': 0, 'total': 0, 'status': 'idle', 'message': ''}

# Template HTML
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generator Prompt Signal Binance</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #718096;
            font-size: 1.1em;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(45deg, #f7fafc, #edf2f7);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .signal-item:hover {
            background: linear-gradient(45deg, #e2e8f0, #cbd5e0);
            transform: scale(1.02);
        }

        .signal-info {
            flex: 1;
        }

        .signal-symbol {
            font-weight: bold;
            font-size: 1.2em;
            color: #2d3748;
        }

        .signal-type {
            padding: 5px 10px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            margin-left: 10px;
        }

        .signal-buy {
            background: linear-gradient(45deg, #48bb78, #38a169);
        }

        .signal-sell {
            background: linear-gradient(45deg, #f56565, #e53e3e);
        }

        .loading {
            text-align: center;
            padding: 50px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .analysis-result {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
        }

        .chart-container {
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            padding: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .signal-item {
                flex-direction: column;
                text-align: center;
            }

            .signal-info {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Generator Prompt Signal Binance</h1>
            <p>Pencari Peluang Trading Cryptocurrency Futures</p>
        </div>

        <div id="main-content">
            <div class="card">
                <h2>🎯 Mulai Analisis</h2>
                <p>Klik tombol di bawah untuk menganalisis semua pasangan futures Binance dan menemukan 10 sinyal terkuat.</p>
                <br>
                <button class="btn" onclick="startAnalysis()">📊 Mulai Analisis</button>
            </div>
        </div>
    </div>

    <script>
        let analysisResults = [];

        async function startAnalysis() {
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="card loading">
                    <div class="spinner"></div>
                    <h3>🔍 Menganalisis Pasangan Futures...</h3>
                    <p id="progress-message">Mohon tunggu, sedang menganalisis semua pasangan trading...</p>
                    <div id="progress-bar" style="width: 100%; background: #e2e8f0; border-radius: 10px; margin: 10px 0;">
                        <div id="progress-fill" style="width: 0%; height: 20px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 10px; transition: width 0.3s ease;"></div>
                    </div>
                    <p id="progress-text">0%</p>
                </div>
            `;

            try {
                // Start analysis
                const analysisPromise = fetch('/analyze', { method: 'POST' });

                // Poll progress
                const progressInterval = setInterval(async () => {
                    try {
                        const progressResponse = await fetch('/progress');
                        const progressData = await progressResponse.json();

                        const progressMessage = document.getElementById('progress-message');
                        const progressFill = document.getElementById('progress-fill');
                        const progressText = document.getElementById('progress-text');

                        if (progressMessage) progressMessage.textContent = progressData.message;

                        if (progressData.total > 0) {
                            const percentage = (progressData.current / progressData.total) * 100;
                            if (progressFill) progressFill.style.width = percentage + '%';
                            if (progressText) progressText.textContent = Math.round(percentage) + '%';
                        }

                        if (progressData.status === 'completed' || progressData.status === 'error') {
                            clearInterval(progressInterval);
                        }
                    } catch (e) {
                        console.log('Progress polling error:', e);
                    }
                }, 1000);

                const response = await analysisPromise;
                clearInterval(progressInterval);

                const data = await response.json();

                if (data.success) {
                    analysisResults = data.results;
                    showSignalList();
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('Terjadi kesalahan saat menganalisis: ' + error.message);
            }
        }

        function showSignalList() {
            const mainContent = document.getElementById('main-content');
            let html = `
                <div class="card">
                    <h2>🎯 10 Sinyal Terkuat</h2>
                    <p>Pilih salah satu pasangan untuk analisis AI mendalam:</p>
                </div>
            `;

            analysisResults.forEach((signal, index) => {
                const signalClass = signal.signal === 'BUY' ? 'signal-buy' : 'signal-sell';
                html += `
                    <div class="card signal-item" onclick="selectSignal(${index})">
                        <div class="signal-info">
                            <div class="signal-symbol">${signal.symbol}</div>
                            <div>Harga: $${signal.current_price.toFixed(4)}</div>
                            <div>Perubahan 24h: ${signal.price_change_24h.toFixed(2)}%</div>
                            <div>Confidence: ${signal.confidence.toFixed(1)}%</div>
                        </div>
                        <div class="signal-type ${signalClass}">${signal.signal}</div>
                    </div>
                `;
            });

            html += `
                <div class="card">
                    <button class="btn" onclick="startAnalysis()">🔄 Ulangi Analisis</button>
                </div>
            `;

            mainContent.innerHTML = html;
        }

        async function selectSignal(index) {
            const signal = analysisResults[index];
            const mainContent = document.getElementById('main-content');

            mainContent.innerHTML = `
                <div class="card loading">
                    <div class="spinner"></div>
                    <h3>🤖 Menganalisis dengan AI...</h3>
                    <p>Sedang menghasilkan analisis mendalam untuk ${signal.symbol}...</p>
                </div>
            `;

            try {
                const response = await fetch('/ai-analysis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ signal_index: index })
                });
                const data = await response.json();

                if (data.success) {
                    showAIAnalysis(signal, data.analysis, data.chart);
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('Terjadi kesalahan saat analisis AI: ' + error.message);
            }
        }

        function showAIAnalysis(signal, analysis, chartData) {
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="card">
                    <h2>🤖 Analisis AI untuk ${signal.symbol}</h2>
                    <div class="analysis-result">${analysis}</div>
                </div>

                <div class="card">
                    <h3>📈 Chart Real-time</h3>
                    <div class="chart-container" id="chart"></div>
                </div>

                <div class="card">
                    <button class="btn" onclick="showSignalList()">📋 Menu Signal</button>
                    <button class="btn" onclick="startAnalysis()">🔄 Ulangi Analisis</button>
                </div>
            `;

            if (chartData) {
                Plotly.newPlot('chart', JSON.parse(chartData).data, JSON.parse(chartData).layout);
            }
        }

        function showError(message) {
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="card">
                    <h2>❌ Error</h2>
                    <p>${message}</p>
                    <br>
                    <button class="btn" onclick="location.reload()">🔄 Muat Ulang</button>
                </div>
            `;
        }
    </script>
</body>
</html>
"""

# Flask Routes
@app.route('/')
def index():
    """Halaman utama aplikasi"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/analyze', methods=['POST'])
def analyze():
    """Endpoint untuk menganalisis semua pasangan"""
    global analysis_progress

    try:
        # Reset progress
        analysis_progress.update({
            'current': 0,
            'total': 0,
            'status': 'running',
            'message': 'Memulai analisis...'
        })

        logging.info("Memulai analisis semua pasangan...")

        # Jalankan analisis di thread terpisah
        def run_analysis():
            try:
                results = analyzer.analyze_all_pairs()
                analysis_progress.update({
                    'status': 'completed',
                    'message': f'Selesai - {len(results)} sinyal ditemukan'
                })
                return results
            except Exception as e:
                analysis_progress.update({
                    'status': 'error',
                    'message': f'Error: {str(e)}'
                })
                raise

        results = run_analysis()

        return jsonify({
            'success': True,
            'results': results,
            'message': f'Berhasil menganalisis {len(results)} sinyal'
        })

    except Exception as e:
        logging.error(f"Error dalam analisis: {e}")
        analysis_progress.update({
            'status': 'error',
            'message': f'Error: {str(e)}'
        })
        return jsonify({
            'success': False,
            'error': f'Terjadi kesalahan: {str(e)}'
        })

@app.route('/progress')
def get_progress():
    """Endpoint untuk mendapatkan progress analisis"""
    return jsonify(analysis_progress)

@app.route('/ai-analysis', methods=['POST'])
def ai_analysis():
    """Endpoint untuk analisis AI"""
    try:
        data = request.get_json()
        signal_index = data.get('signal_index', 0)

        if signal_index >= len(analyzer.analysis_results):
            return jsonify({
                'success': False,
                'error': 'Index sinyal tidak valid'
            })

        selected_signal = analyzer.analysis_results[signal_index]

        # Generate prompt untuk AI
        prompt = analyzer.generate_ai_prompt(selected_signal)

        # Dapatkan analisis dari AI
        ai_analysis = analyzer.get_ai_analysis(prompt)

        # Buat chart
        chart_data = analyzer.create_chart(selected_signal['symbol'], ai_analysis)

        return jsonify({
            'success': True,
            'analysis': ai_analysis,
            'chart': chart_data,
            'prompt': prompt
        })

    except Exception as e:
        print(f"Error dalam analisis AI: {e}")
        return jsonify({
            'success': False,
            'error': f'Terjadi kesalahan: {str(e)}'
        })

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'gemini_available': model is not None
    })

def main():
    """Fungsi utama untuk menjalankan aplikasi"""
    print("🚀 Memulai Generator Prompt Signal Binance...")
    print("📊 Aplikasi web responsif untuk analisis trading cryptocurrency")
    print("🔗 Akses aplikasi di: http://localhost:5000")
    print("⚠️  Pastikan API key Gemini sudah dikonfigurasi di environment variable GEMINI_API_KEY")
    print("=" * 60)

    # Test koneksi Binance
    try:
        test_pairs = analyzer.get_futures_pairs()
        print(f"✅ Koneksi Binance berhasil - {len(test_pairs)} pasangan tersedia")
    except Exception as e:
        print(f"❌ Error koneksi Binance: {e}")

    # Test koneksi Gemini
    if model:
        print("✅ Gemini AI siap digunakan")
    else:
        print("⚠️  Gemini AI tidak tersedia - set GEMINI_API_KEY")

    print("=" * 60)

    # Jalankan aplikasi Flask
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )

if __name__ == "__main__":
    main()
