#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deployment script untuk Generator Prompt Signal Binance
Untuk production deployment dengan optimasi
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_system():
    """Cek sistem dan requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required. Current: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check OS
    os_name = platform.system()
    print(f"✅ OS: {os_name}")
    
    # Check available memory
    try:
        if os_name == "Linux":
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                for line in meminfo.split('\n'):
                    if 'MemTotal:' in line:
                        mem_kb = int(line.split()[1])
                        mem_gb = mem_kb / 1024 / 1024
                        print(f"✅ Memory: {mem_gb:.1f} GB")
                        break
    except:
        print("⚠️  Could not check memory")
    
    return True

def install_production_deps():
    """Install dependencies untuk production"""
    print("📦 Installing production dependencies...")
    
    try:
        # Install requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        
        # Install additional production packages
        prod_packages = [
            "gunicorn",  # WSGI server
            "gevent",    # Async support
            "psutil",    # System monitoring
        ]
        
        for package in prod_packages:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ])
        
        print("✅ Production dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_production_config():
    """Buat konfigurasi production"""
    print("⚙️  Creating production configuration...")
    
    config_content = """# Production Environment Variables
FLASK_ENV=production
DEBUG=False
MAX_PAIRS=200
MAX_WORKERS=12
ANALYSIS_TIMEOUT=60
CACHE_TIMEOUT=600
LOG_LEVEL=WARNING

# Set your API keys here
# GEMINI_API_KEY=your_actual_api_key_here

# Optional: Database URL for session storage
# DATABASE_URL=sqlite:///app.db

# Optional: Redis URL for caching
# REDIS_URL=redis://localhost:6379/0
"""
    
    with open('.env.production', 'w') as f:
        f.write(config_content)
    
    print("✅ Production config created (.env.production)")
    return True

def create_systemd_service():
    """Buat systemd service untuk Linux"""
    if platform.system() != "Linux":
        print("⚠️  Systemd service only for Linux")
        return True
    
    print("🔧 Creating systemd service...")
    
    current_dir = Path.cwd()
    python_path = sys.executable
    
    service_content = f"""[Unit]
Description=Generator Prompt Signal Binance
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={current_dir}
Environment=PATH={current_dir}
ExecStart={python_path} -m gunicorn --bind 0.0.0.0:5000 --workers 4 --worker-class gevent --worker-connections 1000 generator_prompt_signal_binance:app
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path('/tmp/binance-signal.service')
    with open(service_file, 'w') as f:
        f.write(service_content)
    
    print(f"✅ Systemd service created: {service_file}")
    print("To install:")
    print(f"sudo cp {service_file} /etc/systemd/system/")
    print("sudo systemctl daemon-reload")
    print("sudo systemctl enable binance-signal")
    print("sudo systemctl start binance-signal")
    
    return True

def create_nginx_config():
    """Buat konfigurasi Nginx"""
    print("🌐 Creating Nginx configuration...")
    
    nginx_content = """server {
    listen 80;
    server_name your-domain.com;  # Change this to your domain
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files (if any)
    location /static {
        alias /path/to/your/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
"""
    
    with open('/tmp/binance-signal-nginx.conf', 'w') as f:
        f.write(nginx_content)
    
    print("✅ Nginx config created: /tmp/binance-signal-nginx.conf")
    print("To install:")
    print("sudo cp /tmp/binance-signal-nginx.conf /etc/nginx/sites-available/binance-signal")
    print("sudo ln -s /etc/nginx/sites-available/binance-signal /etc/nginx/sites-enabled/")
    print("sudo nginx -t")
    print("sudo systemctl reload nginx")
    
    return True

def create_docker_files():
    """Buat Docker files"""
    print("🐳 Creating Docker configuration...")
    
    # Dockerfile
    dockerfile_content = """FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn gevent

# Copy application
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:5000/health || exit 1

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--worker-class", "gevent", "generator_prompt_signal_binance:app"]
"""
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    
    # Docker Compose
    compose_content = """version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    restart: unless-stopped
"""
    
    with open('docker-compose.yml', 'w') as f:
        f.write(compose_content)
    
    print("✅ Docker files created")
    return True

def run_tests():
    """Jalankan tests sebelum deployment"""
    print("🧪 Running tests...")
    
    try:
        result = subprocess.run([sys.executable, 'test_app.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def main():
    """Main deployment function"""
    print("🚀 GENERATOR PROMPT SIGNAL BINANCE - DEPLOYMENT")
    print("=" * 60)
    
    steps = [
        ("System Check", check_system),
        ("Install Dependencies", install_production_deps),
        ("Run Tests", run_tests),
        ("Production Config", create_production_config),
        ("Systemd Service", create_systemd_service),
        ("Nginx Config", create_nginx_config),
        ("Docker Files", create_docker_files),
    ]
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        if not step_func():
            print(f"❌ {step_name} failed!")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 DEPLOYMENT READY!")
    print("=" * 60)
    
    print("\nNext steps:")
    print("1. Set your GEMINI_API_KEY in .env.production")
    print("2. Choose deployment method:")
    print("   - Direct: python generator_prompt_signal_binance.py")
    print("   - Gunicorn: gunicorn --bind 0.0.0.0:5000 generator_prompt_signal_binance:app")
    print("   - Docker: docker-compose up -d")
    print("   - Systemd: Follow systemd service instructions above")
    print("\n3. Configure reverse proxy (Nginx) if needed")
    print("4. Set up SSL certificate for HTTPS")
    print("5. Configure firewall and security")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
