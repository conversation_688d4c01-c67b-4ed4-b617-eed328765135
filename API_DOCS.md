# 📚 API Documentation - Generator Prompt Signal Binance

## Base URL
```
http://localhost:5000
```

## Endpoints

### 1. Home Page
```
GET /
```
**Deskripsi:** Halaman utama aplikasi web

**Response:** HTML page dengan interface lengkap

---

### 2. Analyze All Pairs
```
POST /analyze
```
**Deskripsi:** Menganalisis semua pasangan futures Binance dan mengembalikan 10 sinyal terkuat

**Request Body:** Tidak ada

**Response:**
```json
{
  "success": true,
  "results": [
    {
      "symbol": "BTCUSDT",
      "signal": "BUY",
      "confidence": 78.5,
      "current_price": 43250.50,
      "price_change_24h": 2.45,
      "timeframe_data": {
        "1h": {
          "indicators": {
            "rsi": 45.2,
            "macd": 0.0012,
            "sma_20": 43100.0,
            "bb_upper": 43500.0,
            "bb_lower": 42800.0
          },
          "support_resistance": {
            "support": [42800, 42500, 42200],
            "resistance": [43500, 43800, 44000]
          },
          "patterns": ["Uptrend", "Hammer"],
          "order_blocks": {
            "bullish_ob": {
              "high": 43200,
              "low": 43000,
              "timestamp": "2024-01-01T12:00:00"
            },
            "bearish_ob": null
          }
        }
      },
      "bullish_signals": 15,
      "bearish_signals": 8,
      "total_signals": 23
    }
  ],
  "message": "Berhasil menganalisis 10 sinyal"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Terjadi kesalahan: [error message]"
}
```

---

### 3. Get Analysis Progress
```
GET /progress
```
**Deskripsi:** Mendapatkan progress real-time dari analisis yang sedang berjalan

**Response:**
```json
{
  "current": 45,
  "total": 100,
  "status": "running",
  "message": "Menganalisis ETHUSDT..."
}
```

**Status Values:**
- `idle`: Tidak ada analisis yang berjalan
- `running`: Analisis sedang berjalan
- `completed`: Analisis selesai
- `error`: Terjadi error

---

### 4. AI Analysis
```
POST /ai-analysis
```
**Deskripsi:** Mendapatkan analisis mendalam dari AI untuk pasangan yang dipilih

**Request Body:**
```json
{
  "signal_index": 0
}
```

**Response:**
```json
{
  "success": true,
  "analysis": "🎯 SINYAL TRADING BTCUSDT\n\n📊 **KEPUTUSAN:** BUY\n💰 **ENTRY POINT:** $43,250\n📈 **CONFIDENCE:** 78%\n\n💼 **SETUP TRADING (Modal 3 Juta IDR):**\n🔴 Stop Loss: $42,800\n🟢 Take Profit: $44,500\n⚡ Leverage: 5x\n📏 Size Posisi: 300 USDT\n\n📋 **ANALISIS:**\n[Detailed analysis...]\n\n🤖 Signal Analyzer AI by bobacheese",
  "chart": "{\"data\": [...], \"layout\": {...}}",
  "prompt": "[Generated prompt for AI]"
}
```

---

### 5. Health Check
```
GET /health
```
**Deskripsi:** Cek status kesehatan aplikasi

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "gemini_available": true
}
```

---

## Data Models

### Signal Object
```json
{
  "symbol": "string",           // Nama pasangan (e.g., "BTCUSDT")
  "signal": "string",           // "BUY", "SELL", atau "NEUTRAL"
  "confidence": "number",       // Confidence score 0-100
  "current_price": "number",    // Harga saat ini
  "price_change_24h": "number", // Perubahan harga 24 jam (%)
  "timeframe_data": "object",   // Data untuk setiap timeframe
  "bullish_signals": "number",  // Jumlah sinyal bullish
  "bearish_signals": "number",  // Jumlah sinyal bearish
  "total_signals": "number"     // Total sinyal yang dianalisis
}
```

### Indicators Object
```json
{
  "rsi": "number",              // RSI (0-100)
  "macd": "number",             // MACD line
  "macd_signal": "number",      // MACD signal line
  "macd_histogram": "number",   // MACD histogram
  "sma_20": "number",           // Simple Moving Average 20
  "sma_50": "number",           // Simple Moving Average 50
  "ema_12": "number",           // Exponential Moving Average 12
  "ema_26": "number",           // Exponential Moving Average 26
  "bb_upper": "number",         // Bollinger Band upper
  "bb_lower": "number",         // Bollinger Band lower
  "bb_middle": "number",        // Bollinger Band middle
  "stoch_k": "number",          // Stochastic %K
  "stoch_d": "number",          // Stochastic %D
  "williams_r": "number",       // Williams %R
  "adx": "number",              // Average Directional Index
  "cci": "number",              // Commodity Channel Index
  "atr": "number",              // Average True Range
  "mfi": "number",              // Money Flow Index
  "volume_sma": "number",       // Volume SMA
  "current_price": "number",    // Harga saat ini
  "price_change_24h": "number"  // Perubahan 24 jam
}
```

### Support/Resistance Object
```json
{
  "support": ["number"],        // Array level support
  "resistance": ["number"]      // Array level resistance
}
```

### Order Block Object
```json
{
  "bullish_ob": {
    "high": "number",           // High price
    "low": "number",            // Low price
    "timestamp": "string"       // ISO timestamp
  },
  "bearish_ob": {
    "high": "number",
    "low": "number", 
    "timestamp": "string"
  }
}
```

---

## Error Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 400  | Bad Request - Invalid parameters |
| 500  | Internal Server Error |
| 503  | Service Unavailable - API limits exceeded |

---

## Rate Limits

- **Binance API:** Mengikuti rate limit Binance (1200 requests/minute)
- **Gemini AI:** Mengikuti rate limit Google AI
- **Analysis:** Maksimal 1 analisis penuh per 5 menit

---

## Usage Examples

### JavaScript (Frontend)
```javascript
// Start analysis
const response = await fetch('/analyze', { method: 'POST' });
const data = await response.json();

// Get progress
const progress = await fetch('/progress');
const progressData = await progress.json();

// Get AI analysis
const aiResponse = await fetch('/ai-analysis', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ signal_index: 0 })
});
```

### Python
```python
import requests

# Start analysis
response = requests.post('http://localhost:5000/analyze')
data = response.json()

# Get AI analysis
ai_response = requests.post('http://localhost:5000/ai-analysis', 
                           json={'signal_index': 0})
ai_data = ai_response.json()
```

### cURL
```bash
# Start analysis
curl -X POST http://localhost:5000/analyze

# Get progress
curl http://localhost:5000/progress

# AI analysis
curl -X POST http://localhost:5000/ai-analysis \
  -H "Content-Type: application/json" \
  -d '{"signal_index": 0}'
```
