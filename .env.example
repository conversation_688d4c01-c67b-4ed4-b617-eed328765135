# Generator Prompt Signal Binance - Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Gemini AI API Key (Required for AI analysis)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Flask Environment (development/production/testing)
FLASK_ENV=development

# Debug mode (True/False)
DEBUG=True

# Application host and port
HOST=0.0.0.0
PORT=5000

# Secret key for Flask sessions
SECRET_KEY=binance_signal_generator_2024_change_this_in_production

# =============================================================================
# ANALYSIS CONFIGURATION
# =============================================================================

# Maximum number of trading pairs to analyze
# Reduce this number if you have limited resources
MAX_PAIRS=100

# Number of worker threads for parallel analysis
# Adjust based on your system capabilities
MAX_WORKERS=8

# Timeout for each pair analysis (seconds)
ANALYSIS_TIMEOUT=45

# Cache timeout for market data (seconds)
CACHE_TIMEOUT=300

# Minimum confidence threshold for signals (0-100)
CONFIDENCE_THRESHOLD=65

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=binance_analyzer.log

# =============================================================================
# TECHNICAL INDICATORS CONFIGURATION
# =============================================================================

# RSI settings
RSI_PERIOD=14
RSI_OVERSOLD=30
RSI_OVERBOUGHT=70

# Moving Average settings
SMA_SHORT=20
SMA_LONG=50
EMA_FAST=12
EMA_SLOW=26

# Bollinger Bands settings
BOLLINGER_PERIOD=20

# Stochastic settings
STOCH_K_PERIOD=14
STOCH_D_PERIOD=3
STOCH_OVERSOLD=20
STOCH_OVERBOUGHT=80

# =============================================================================
# ADVANCED CONFIGURATION (Optional)
# =============================================================================

# Custom timeframes (comma-separated)
# Default: 1h,30m,15m
# TIMEFRAMES=4h,1h,30m,15m

# Database URL (for session storage - optional)
# DATABASE_URL=sqlite:///app.db

# Redis URL (for caching - optional)
# REDIS_URL=redis://localhost:6379/0

# Binance API base URL (usually don't change this)
# BINANCE_BASE_URL=https://fapi.binance.com

# =============================================================================
# PRODUCTION SETTINGS (Uncomment for production)
# =============================================================================

# FLASK_ENV=production
# DEBUG=False
# MAX_PAIRS=200
# MAX_WORKERS=12
# ANALYSIS_TIMEOUT=60
# CACHE_TIMEOUT=600
# LOG_LEVEL=WARNING

# =============================================================================
# SECURITY SETTINGS (Production)
# =============================================================================

# HTTPS settings (for production with SSL)
# FORCE_HTTPS=True
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# CORS settings (if needed for API access)
# CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Rate limiting (requests per minute)
# RATE_LIMIT=100

# =============================================================================
# MONITORING & ALERTS (Optional)
# =============================================================================

# Email notifications (optional)
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
# ALERT_EMAIL=<EMAIL>

# Webhook notifications (optional)
# WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
# DISCORD_WEBHOOK=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real API keys to version control
# 2. Use strong, unique SECRET_KEY in production
# 3. Adjust MAX_PAIRS and MAX_WORKERS based on your system resources
# 4. Monitor API usage to avoid rate limits
# 5. Use HTTPS in production environments
# 6. Regular backup of configuration and logs
# 7. Keep API keys secure and rotate them regularly

# =============================================================================
# EXAMPLE VALUES FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# Development (Local testing)
# MAX_PAIRS=20
# MAX_WORKERS=4
# DEBUG=True
# LOG_LEVEL=DEBUG

# Staging (Pre-production testing)
# MAX_PAIRS=50
# MAX_WORKERS=6
# DEBUG=False
# LOG_LEVEL=INFO

# Production (Live environment)
# MAX_PAIRS=200
# MAX_WORKERS=12
# DEBUG=False
# LOG_LEVEL=WARNING
# FORCE_HTTPS=True
