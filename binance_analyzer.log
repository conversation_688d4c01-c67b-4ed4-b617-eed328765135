2025-08-04 20:25:29,334 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-04 20:25:29,334 - INFO - [33mPress CTRL+C to quit[0m
2025-08-04 20:25:50,933 - INFO - 127.0.0.1 - - [04/Aug/2025 20:25:50] "GET / HTTP/1.1" 200 -
2025-08-04 20:25:52,766 - INFO - 127.0.0.1 - - [04/Aug/2025 20:25:52] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-04 20:28:17,374 - INFO - Me<PERSON>lai analisis semua pasangan...
2025-08-04 20:28:17,722 - INFO - Me<PERSON>lai analisis 100 pasangan...
2025-08-04 20:28:18,387 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:18] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:19,406 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:19] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:20,323 - INFO - Progress: 10/100 (10.0%)
2025-08-04 20:28:20,383 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:20] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:21,390 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:21] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:21,680 - INFO - Progress: 20/100 (20.0%)
2025-08-04 20:28:22,398 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:22] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:23,065 - INFO - Progress: 30/100 (30.0%)
2025-08-04 20:28:23,389 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:23] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:24,401 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:24] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:24,694 - INFO - Progress: 40/100 (40.0%)
2025-08-04 20:28:25,379 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:25] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:26,075 - INFO - Progress: 50/100 (50.0%)
2025-08-04 20:28:26,402 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:26] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:27,409 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:27] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:27,533 - INFO - Progress: 60/100 (60.0%)
2025-08-04 20:28:28,387 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:28] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:29,404 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:29] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:29,525 - INFO - Progress: 70/100 (70.0%)
2025-08-04 20:28:30,379 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:30] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:30,934 - INFO - Progress: 80/100 (80.0%)
2025-08-04 20:28:31,413 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:31] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:32,384 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:32] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:32,941 - INFO - Progress: 90/100 (90.0%)
2025-08-04 20:28:33,406 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:33] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:28:34,141 - INFO - Progress: 100/100 (100.0%)
2025-08-04 20:28:34,142 - INFO - Analisis selesai. Ditemukan 0 sinyal, menampilkan 10 terkuat
2025-08-04 20:28:34,170 - INFO - 127.0.0.1 - - [04/Aug/2025 20:28:34] "POST /analyze HTTP/1.1" 200 -
2025-08-04 20:28:53,275 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-04 20:28:53,275 - INFO - [33mPress CTRL+C to quit[0m
2025-08-04 20:29:07,577 - INFO - Memulai analisis semua pasangan...
2025-08-04 20:29:07,710 - INFO - Memulai analisis 100 pasangan...
2025-08-04 20:29:08,593 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:08] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:09,608 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:09] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:10,606 - INFO - Progress: 10/100 (10.0%)
2025-08-04 20:29:10,683 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:10] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:11,611 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:11] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:12,485 - INFO - Progress: 20/100 (20.0%)
2025-08-04 20:29:12,609 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:12] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:13,590 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:13] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:14,513 - INFO - Progress: 30/100 (30.0%)
2025-08-04 20:29:14,682 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:14] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:15,601 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:15] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:16,541 - INFO - Progress: 40/100 (40.0%)
2025-08-04 20:29:16,694 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:16] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:17,611 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:17] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:18,112 - INFO - Progress: 50/100 (50.0%)
2025-08-04 20:29:18,596 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:18] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:19,614 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:19] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:19,862 - INFO - Progress: 60/100 (60.0%)
2025-08-04 20:29:20,613 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:20] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:21,612 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:21] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:22,117 - INFO - Progress: 70/100 (70.0%)
2025-08-04 20:29:22,621 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:22] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:23,597 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:23] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:24,387 - INFO - Progress: 80/100 (80.0%)
2025-08-04 20:29:24,575 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:24] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:26,127 - INFO - Progress: 90/100 (90.0%)
2025-08-04 20:29:26,266 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:26] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:27,248 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:27] "GET /progress HTTP/1.1" 200 -
2025-08-04 20:29:27,353 - INFO - Progress: 100/100 (100.0%)
2025-08-04 20:29:27,354 - INFO - Analisis selesai. Ditemukan 0 sinyal, menampilkan 10 terkuat
2025-08-04 20:29:27,377 - INFO - 127.0.0.1 - - [04/Aug/2025 20:29:27] "POST /analyze HTTP/1.1" 200 -
2025-08-04 20:31:15,094 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-04 20:31:15,094 - INFO - [33mPress CTRL+C to quit[0m
