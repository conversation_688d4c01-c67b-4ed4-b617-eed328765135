# 🚀 Generator Prompt Signal Binance

Aplikasi web responsif untuk analisis sinyal trading cryptocurrency futures dengan AI integration.

## ✨ Fitur Utama

- 📊 Analisis real-time semua pasangan futures Binance
- 🤖 Integrasi AI Gemini untuk analisis mendalam
- 📈 60+ indikator teknikal dengan parameter optimal
- 🎯 Deteksi support/resistance dan order blocks
- 📱 UI responsif mobile-first dengan desain iOS
- ⚡ Threading untuk performa optimal
- 🔄 Chart interaktif real-time

## 🛠️ Instalasi

1. **Clone atau download file**
```bash
# Download file generator_prompt_signal_binance.py dan requirements.txt
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Setup API Key Gemini**
```bash
# Set environment variable
export GEMINI_API_KEY="your_gemini_api_key_here"

# Atau di Windows
set GEMINI_API_KEY=your_gemini_api_key_here
```

4. **Jalankan aplikasi**
```bash
python generator_prompt_signal_binance.py
```

5. **<PERSON><PERSON><PERSON> aplik<PERSON>**
```
http://localhost:5000
```

## 🎯 Cara Penggunaan

1. **<PERSON><PERSON>lisis**
   - Klik tombol "Mulai Analisis"
   - Aplikasi akan menganalisis semua pasangan futures Binance
   - Menunggu hingga analisis selesai

2. **Pilih Sinyal**
   - Pilih salah satu dari 10 sinyal terkuat
   - Sistem akan menghasilkan analisis AI mendalam

3. **Review Analisis**
   - Lihat rekomendasi BUY/SELL dari AI
   - Review setup trading lengkap
   - Analisis chart real-time

4. **Navigasi**
   - "Menu Signal": Kembali ke daftar sinyal
   - "Ulangi Analisis": Restart analisis penuh

## 📊 Indikator Teknikal

### Trend Indicators
- SMA (20, 50)
- EMA (12, 26)
- MACD & Signal
- ADX
- CCI

### Momentum Indicators
- RSI (14)
- Stochastic (K, D)
- Williams %R
- Money Flow Index (MFI)

### Volatility Indicators
- Bollinger Bands
- Average True Range (ATR)

### Volume Indicators
- Volume SMA
- Money Flow Index

## 🔧 Konfigurasi

### Environment Variables
```bash
GEMINI_API_KEY=your_api_key_here  # Required untuk AI analysis
```

### Pengaturan Default
- **Max Workers**: 10 threads untuk analisis paralel
- **Timeframes**: 1h, 30m, 15m
- **Candlestick Limit**: 250 per timeframe
- **Top Signals**: 10 sinyal terkuat
- **Support/Resistance Window**: 50 candles

## 🎨 Fitur UI

### Desain
- ✨ Animasi futuristik
- 🎨 Warna pastel bertema iOS
- 📱 Responsif mobile dan desktop
- 🃏 Sistem kartu yang dapat diperluas
- 🌙 Dark mode untuk chart

### Komponen
- 📊 Chart interaktif dengan Plotly
- 🎯 Signal cards dengan hover effects
- ⚡ Loading animations
- 📈 Real-time price updates

## 🔒 Keamanan & Performa

### Threading
- Analisis paralel dengan ThreadPoolExecutor
- Timeout protection (30 detik per pair)
- Error handling untuk setiap thread

### API Rate Limiting
- Session reuse untuk Binance API
- Proper headers dan timeout
- Error recovery mechanisms

### Memory Management
- Efficient pandas operations
- Limited data retention
- Garbage collection optimization

## 🚨 Troubleshooting

### Common Issues

1. **Gemini API Error**
```
Error: Gemini AI tidak tersedia
Solution: Set GEMINI_API_KEY environment variable
```

2. **Binance Connection Error**
```
Error: Timeout connecting to Binance
Solution: Check internet connection, try again
```

3. **No Signals Found**
```
Tidak ada sinyal ditemukan
Solution: Market mungkin sideways, coba analisis ulang
```

### Debug Mode
```python
# Ubah di main() function
app.run(debug=True)  # Enable debug mode
```

## 📈 Trading Disclaimer

⚠️ **PERINGATAN PENTING**:
- Aplikasi ini hanya untuk tujuan edukasi dan analisis
- Bukan saran investasi atau trading
- Selalu lakukan riset sendiri (DYOR)
- Trading cryptocurrency berisiko tinggi
- Gunakan manajemen risiko yang tepat

## 🤝 Kontribusi

Dibuat oleh: **bobacheese**

### Features Roadmap
- [ ] WebSocket real-time data
- [ ] Multiple AI providers
- [ ] Backtesting functionality
- [ ] Portfolio tracking
- [ ] Alert notifications

## 📄 License

MIT License - Bebas digunakan untuk tujuan edukasi dan pengembangan.

---

🤖 **Signal Analyzer AI by bobacheese**
