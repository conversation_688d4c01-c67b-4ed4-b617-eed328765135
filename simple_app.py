#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generator Prompt Signal Binance - Simplified Version
Versi sederhana tanpa dependencies kompleks
"""

import sys
import json
import time
from datetime import datetime

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    print("⚠️  requests tidak tersedia - mode offline")

def get_binance_data():
    """Ambil data dari Binance API"""
    if not HAS_REQUESTS:
        return {"error": "requests module not available"}
    
    try:
        url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def analyze_simple():
    """Analisis sederhana"""
    print("🔍 Menganalisis data Binance...")
    
    data = get_binance_data()
    if "error" in data:
        print(f"❌ Error: {data['error']}")
        return []
    
    # Filter dan sort berdasarkan volume
    if isinstance(data, list):
        usdt_pairs = [item for item in data if item['symbol'].endswith('USDT')]
        sorted_pairs = sorted(usdt_pairs, key=lambda x: float(x['volume']), reverse=True)
        
        results = []
        for pair in sorted_pairs[:10]:
            price_change = float(pair['priceChangePercent'])
            signal = "BUY" if price_change > 2 else "SELL" if price_change < -2 else "NEUTRAL"
            
            results.append({
                'symbol': pair['symbol'],
                'price': pair['lastPrice'],
                'change': price_change,
                'volume': pair['volume'],
                'signal': signal
            })
        
        return results
    
    return []

def main():
    """Main function"""
    print("🚀 Generator Prompt Signal Binance - Simple Version")
    print("=" * 60)
    
    if not HAS_REQUESTS:
        print("⚠️  Mode terbatas - install 'requests' untuk fitur lengkap")
        print("Jalankan: python3 -m pip install requests")
        return
    
    while True:
        print("\n1. Analisis Sinyal")
        print("2. Keluar")
        
        choice = input("\nPilih opsi (1-2): ").strip()
        
        if choice == "1":
            results = analyze_simple()
            
            if results:
                print("\n🎯 Top 10 Signals:")
                print("-" * 60)
                for i, result in enumerate(results, 1):
                    print(f"{i:2d}. {result['symbol']:12} | "
                          f"${result['price']:>10} | "
                          f"{result['change']:>6.2f}% | "
                          f"{result['signal']}")
                print("-" * 60)
            else:
                print("❌ Tidak ada data ditemukan")
                
        elif choice == "2":
            print("👋 Terima kasih!")
            break
        else:
            print("❌ Pilihan tidak valid")

if __name__ == "__main__":
    main()
