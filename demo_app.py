#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generator Prompt Signal Binance - Demo Version
Versi demo dengan data mock untuk menunjukkan fungsionalitas
"""

import json
import time
import random
from datetime import datetime, timedelta
from flask import Flask, render_template_string, jsonify, request

app = Flask(__name__)

# Mock data untuk demo
DEMO_PAIRS = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
    'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT',
    'LINKUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT'
]

def generate_mock_indicators():
    """Generate mock technical indicators"""
    return {
        'rsi': random.uniform(20, 80),
        'macd': random.uniform(-0.001, 0.001),
        'macd_signal': random.uniform(-0.001, 0.001),
        'sma_20': random.uniform(40000, 50000),
        'sma_50': random.uniform(39000, 51000),
        'ema_12': random.uniform(40500, 49500),
        'ema_26': random.uniform(40000, 50000),
        'bb_upper': random.uniform(45000, 52000),
        'bb_lower': random.uniform(38000, 45000),
        'bb_middle': random.uniform(41000, 48000),
        'stoch_k': random.uniform(10, 90),
        'stoch_d': random.uniform(10, 90),
        'williams_r': random.uniform(-90, -10),
        'adx': random.uniform(15, 45),
        'cci': random.uniform(-200, 200),
        'atr': random.uniform(500, 2000),
        'mfi': random.uniform(20, 80),
        'volume_sma': random.uniform(1000000, 5000000),
        'current_price': random.uniform(40000, 50000),
        'price_change_24h': random.uniform(-5, 5)
    }

def analyze_mock_signal(symbol):
    """Analyze mock signal for a symbol"""
    indicators = generate_mock_indicators()
    
    # Simple signal logic
    bullish_score = 0
    bearish_score = 0
    
    if indicators['rsi'] < 30:
        bullish_score += 2
    elif indicators['rsi'] > 70:
        bearish_score += 2
    
    if indicators['macd'] > indicators['macd_signal']:
        bullish_score += 1
    else:
        bearish_score += 1
    
    if indicators['current_price'] > indicators['sma_20']:
        bullish_score += 1
    else:
        bearish_score += 1
    
    total_score = bullish_score + bearish_score
    if total_score > 0:
        confidence = max(bullish_score, bearish_score) / total_score * 100
    else:
        confidence = 50
    
    if bullish_score > bearish_score and confidence > 60:
        signal = "BUY"
    elif bearish_score > bullish_score and confidence > 60:
        signal = "SELL"
    else:
        signal = "NEUTRAL"
    
    return {
        'symbol': symbol,
        'signal': signal,
        'confidence': confidence,
        'current_price': indicators['current_price'],
        'price_change_24h': indicators['price_change_24h'],
        'timeframe_data': {
            '1h': {
                'indicators': indicators,
                'support_resistance': {
                    'support': [indicators['current_price'] * 0.98, indicators['current_price'] * 0.96],
                    'resistance': [indicators['current_price'] * 1.02, indicators['current_price'] * 1.04]
                },
                'patterns': ['Uptrend' if indicators['price_change_24h'] > 0 else 'Downtrend'],
                'order_blocks': {'bullish_ob': None, 'bearish_ob': None}
            }
        },
        'bullish_signals': bullish_score,
        'bearish_signals': bearish_score,
        'total_signals': total_score
    }

# HTML Template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generator Prompt Signal Binance - DEMO</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95); padding: 30px;
            border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .header h1 {
            color: #4a5568; font-size: 2.5em; margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
        }
        .demo-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white; padding: 5px 15px; border-radius: 15px;
            font-size: 0.9em; margin-top: 10px; display: inline-block;
        }
        .card {
            background: rgba(255, 255, 255, 0.95); border-radius: 15px;
            padding: 25px; margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white; border: none; padding: 15px 30px;
            border-radius: 25px; font-size: 1.1em; cursor: pointer;
            transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .btn:hover { transform: translateY(-2px); }
        .signal-item {
            display: flex; justify-content: space-between; align-items: center;
            padding: 15px; margin: 10px 0;
            background: linear-gradient(45deg, #f7fafc, #edf2f7);
            border-radius: 10px; cursor: pointer; transition: all 0.3s ease;
        }
        .signal-item:hover { background: linear-gradient(45deg, #e2e8f0, #cbd5e0); }
        .signal-symbol { font-weight: bold; font-size: 1.2em; color: #2d3748; }
        .signal-type {
            padding: 5px 10px; border-radius: 15px; color: white; font-weight: bold;
        }
        .signal-buy { background: linear-gradient(45deg, #48bb78, #38a169); }
        .signal-sell { background: linear-gradient(45deg, #f56565, #e53e3e); }
        .signal-neutral { background: linear-gradient(45deg, #a0aec0, #718096); }
        .loading { text-align: center; padding: 50px; }
        .spinner {
            border: 4px solid #f3f3f3; border-top: 4px solid #667eea;
            border-radius: 50%; width: 50px; height: 50px;
            animation: spin 1s linear infinite; margin: 0 auto 20px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .analysis-result {
            background: #1a202c; color: #e2e8f0; padding: 20px;
            border-radius: 10px; margin: 20px 0; white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Generator Prompt Signal Binance</h1>
            <div class="demo-badge">🎭 DEMO MODE</div>
            <p>Pencari Peluang Trading Cryptocurrency Futures</p>
        </div>
        
        <div id="main-content">
            <div class="card">
                <h2>🎯 Demo Analisis</h2>
                <p>Ini adalah versi demo dengan data simulasi untuk menunjukkan fungsionalitas aplikasi.</p>
                <br>
                <button class="btn" onclick="startDemo()">📊 Mulai Demo Analisis</button>
            </div>
        </div>
    </div>
    
    <script>
        let demoResults = [];
        
        function startDemo() {
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="card loading">
                    <div class="spinner"></div>
                    <h3>🔍 Menganalisis Pasangan Demo...</h3>
                    <p>Simulasi analisis real-time...</p>
                </div>
            `;
            
            // Simulate analysis delay
            setTimeout(() => {
                fetch('/demo-analyze', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            demoResults = data.results;
                            showDemoResults();
                        }
                    });
            }, 3000);
        }
        
        function showDemoResults() {
            const mainContent = document.getElementById('main-content');
            let html = `
                <div class="card">
                    <h2>🎯 10 Sinyal Demo Terkuat</h2>
                    <p>Hasil simulasi analisis (data tidak real):</p>
                </div>
            `;
            
            demoResults.forEach((signal, index) => {
                const signalClass = signal.signal === 'BUY' ? 'signal-buy' : 
                                  signal.signal === 'SELL' ? 'signal-sell' : 'signal-neutral';
                html += `
                    <div class="card signal-item" onclick="showDemoAnalysis(${index})">
                        <div class="signal-info">
                            <div class="signal-symbol">${signal.symbol}</div>
                            <div>Harga: $${signal.current_price.toFixed(2)}</div>
                            <div>Perubahan 24h: ${signal.price_change_24h.toFixed(2)}%</div>
                            <div>Confidence: ${signal.confidence.toFixed(1)}%</div>
                        </div>
                        <div class="signal-type ${signalClass}">${signal.signal}</div>
                    </div>
                `;
            });
            
            html += `
                <div class="card">
                    <button class="btn" onclick="startDemo()">🔄 Ulangi Demo</button>
                </div>
            `;
            
            mainContent.innerHTML = html;
        }
        
        function showDemoAnalysis(index) {
            const signal = demoResults[index];
            const analysis = generateDemoAnalysis(signal);
            
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="card">
                    <h2>🤖 Demo Analisis AI untuk ${signal.symbol}</h2>
                    <div class="analysis-result">${analysis}</div>
                </div>
                
                <div class="card">
                    <button class="btn" onclick="showDemoResults()">📋 Kembali ke Sinyal</button>
                    <button class="btn" onclick="startDemo()">🔄 Ulangi Demo</button>
                </div>
            `;
        }
        
        function generateDemoAnalysis(signal) {
            return `🎯 SINYAL TRADING ${signal.symbol} (DEMO)

📊 **KEPUTUSAN:** ${signal.signal}
💰 **ENTRY POINT:** $${signal.current_price.toFixed(2)}
📈 **CONFIDENCE:** ${signal.confidence.toFixed(1)}%

💼 **SETUP TRADING (Modal 3 Juta IDR):**
🔴 Stop Loss: $${(signal.current_price * 0.97).toFixed(2)}
🟢 Take Profit: $${(signal.current_price * 1.05).toFixed(2)}
⚡ Leverage: 3x
📏 Size Posisi: 500 USDT

📋 **ANALISIS DEMO:**
Berdasarkan simulasi 60+ indikator teknikal, ${signal.symbol} menunjukkan 
sinyal ${signal.signal} dengan confidence ${signal.confidence.toFixed(1)}%.

RSI: ${signal.timeframe_data['1h'].indicators.rsi.toFixed(2)}
MACD: ${signal.timeframe_data['1h'].indicators.macd.toFixed(6)}
Support: $${signal.timeframe_data['1h'].support_resistance.support[0].toFixed(2)}
Resistance: $${signal.timeframe_data['1h'].support_resistance.resistance[0].toFixed(2)}

⚠️ **CATATAN:** Ini adalah data simulasi untuk demo aplikasi.

---
🤖 Signal Analyzer AI by bobacheese (DEMO MODE)`;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/demo-analyze', methods=['POST'])
def demo_analyze():
    """Demo analysis endpoint"""
    results = []
    
    for symbol in DEMO_PAIRS:
        result = analyze_mock_signal(symbol)
        if result['signal'] != 'NEUTRAL':
            results.append(result)
    
    # Sort by confidence
    results.sort(key=lambda x: x['confidence'], reverse=True)
    
    return jsonify({
        'success': True,
        'results': results[:10],
        'message': f'Demo: {len(results)} sinyal ditemukan'
    })

if __name__ == '__main__':
    print("🎭 GENERATOR PROMPT SIGNAL BINANCE - DEMO MODE")
    print("=" * 60)
    print("🌐 Demo aplikasi tersedia di: http://localhost:5001")
    print("📊 Menggunakan data simulasi untuk demonstrasi")
    print("⚠️  Data tidak real - hanya untuk demo fungsionalitas")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=5001, debug=False)
