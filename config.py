#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Konfigurasi untuk Generator Prompt Signal Binance
"""

import os
from pathlib import Path

class Config:
    """Konfigurasi dasar aplikasi"""
    
    # API Configuration
    BINANCE_BASE_URL = "https://fapi.binance.com"
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'YOUR_GEMINI_API_KEY_HERE')
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'binance_signal_generator_2024')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 5000))
    
    # Analysis Configuration
    MAX_PAIRS = int(os.getenv('MAX_PAIRS', 100))  # Maksimal pasangan untuk dianalisis
    MAX_WORKERS = int(os.getenv('MAX_WORKERS', 8))  # Thread workers
    ANALYSIS_TIMEOUT = int(os.getenv('ANALYSIS_TIMEOUT', 45))  # Timeout per pair (detik)
    CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', 300))  # Cache timeout (detik)
    
    # Timeframes untuk analisis
    TIMEFRAMES = ['1h', '30m', '15m']
    
    # Indikator configuration
    RSI_PERIOD = 14
    SMA_SHORT = 20
    SMA_LONG = 50
    EMA_FAST = 12
    EMA_SLOW = 26
    BOLLINGER_PERIOD = 20
    STOCH_K_PERIOD = 14
    STOCH_D_PERIOD = 3
    
    # Signal thresholds
    RSI_OVERSOLD = 30
    RSI_OVERBOUGHT = 70
    STOCH_OVERSOLD = 20
    STOCH_OVERBOUGHT = 80
    CONFIDENCE_THRESHOLD = 65  # Minimum confidence untuk sinyal
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = 'binance_analyzer.log'
    
    # File paths
    BASE_DIR = Path(__file__).parent
    STATIC_DIR = BASE_DIR / 'static'
    TEMPLATES_DIR = BASE_DIR / 'templates'
    
    @classmethod
    def load_from_env(cls):
        """Load konfigurasi dari file .env"""
        env_file = cls.BASE_DIR / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value

class DevelopmentConfig(Config):
    """Konfigurasi untuk development"""
    DEBUG = True
    MAX_PAIRS = 20  # Lebih sedikit untuk testing
    MAX_WORKERS = 4

class ProductionConfig(Config):
    """Konfigurasi untuk production"""
    DEBUG = False
    MAX_PAIRS = 200  # Lebih banyak untuk production
    MAX_WORKERS = 12

class TestingConfig(Config):
    """Konfigurasi untuk testing"""
    DEBUG = True
    MAX_PAIRS = 5
    MAX_WORKERS = 2
    ANALYSIS_TIMEOUT = 10

# Pilih konfigurasi berdasarkan environment
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """Dapatkan konfigurasi berdasarkan environment"""
    env = os.getenv('FLASK_ENV', 'default')
    return config_map.get(env, DevelopmentConfig)
