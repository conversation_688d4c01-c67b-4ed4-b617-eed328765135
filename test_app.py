#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script untuk Generator Prompt Signal Binance
"""

import sys
import time
import requests
import json
from pathlib import Path

def test_binance_connection():
    """Test koneksi ke Binance API"""
    print("🔍 Testing Binance API connection...")
    
    try:
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        symbols = [s['symbol'] for s in data['symbols'] if s['status'] == 'TRADING']
        
        print(f"✅ Binance API OK - {len(symbols)} trading pairs available")
        return True
        
    except Exception as e:
        print(f"❌ Binance API Error: {e}")
        return False

def test_gemini_api():
    """Test Gemini AI API"""
    print("🤖 Testing Gemini AI API...")
    
    try:
        import google.generativeai as genai
        import os
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key or api_key == 'YOUR_GEMINI_API_KEY_HERE':
            print("⚠️  Gemini API key not configured")
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')
        
        # Test simple request
        response = model.generate_content("Hello, test message")
        
        if response.text:
            print("✅ Gemini AI OK")
            return True
        else:
            print("❌ Gemini AI: No response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini AI Error: {e}")
        return False

def test_dependencies():
    """Test semua dependencies"""
    print("📦 Testing dependencies...")
    
    required_packages = [
        'flask', 'requests', 'pandas', 'numpy', 'ta', 
        'google.generativeai', 'plotly'
    ]
    
    missing = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Missing packages: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies OK")
    return True

def test_app_startup():
    """Test startup aplikasi"""
    print("🚀 Testing app startup...")
    
    try:
        # Import main app
        from generator_prompt_signal_binance import analyzer, app
        
        # Test analyzer initialization
        if hasattr(analyzer, 'session'):
            print("✅ Analyzer initialized")
        else:
            print("❌ Analyzer initialization failed")
            return False
        
        # Test Flask app
        if app:
            print("✅ Flask app initialized")
        else:
            print("❌ Flask app initialization failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App startup error: {e}")
        return False

def test_basic_analysis():
    """Test analisis dasar"""
    print("📊 Testing basic analysis...")
    
    try:
        from generator_prompt_signal_binance import analyzer
        
        # Test get futures pairs
        pairs = analyzer.get_futures_pairs()
        if pairs:
            print(f"✅ Got {len(pairs)} futures pairs")
        else:
            print("❌ No futures pairs found")
            return False
        
        # Test get klines for one pair
        if pairs:
            test_symbol = pairs[0]
            df = analyzer.get_klines(test_symbol, '1h', 50)
            
            if not df.empty:
                print(f"✅ Klines data OK for {test_symbol}")
            else:
                print(f"❌ No klines data for {test_symbol}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic analysis error: {e}")
        return False

def run_all_tests():
    """Jalankan semua test"""
    print("=" * 60)
    print("🧪 GENERATOR PROMPT SIGNAL BINANCE - SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Binance API", test_binance_connection),
        ("Gemini AI", test_gemini_api),
        ("App Startup", test_app_startup),
        ("Basic Analysis", test_basic_analysis)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nSummary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! App ready to run.")
        return True
    else:
        print("⚠️  Some tests failed. Please fix issues before running.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
