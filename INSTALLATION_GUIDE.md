# 📖 Panduan Instalasi Lengkap - Generator Prompt Signal Binance

## 🎯 Persyaratan Sistem

### Minimum Requirements
- **Python:** 3.8 atau lebih baru
- **RAM:** 2 GB (4 GB recommended)
- **Storage:** 1 GB free space
- **Internet:** Koneksi stabil untuk API calls

### Supported Operating Systems
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Ubuntu 18.04+
- ✅ CentOS 7+
- ✅ Debian 10+

## 🚀 Instalasi Cepat

### Metode 1: One-Click Setup (Recommended)

#### Windows
1. Download semua file ke folder
2. Double-click `start.bat`
3. <PERSON><PERSON><PERSON> instruksi di layar

#### Linux/Mac
1. Download semua file ke folder
2. Buka terminal di folder tersebut
3. Jalankan: `chmod +x start.sh && ./start.sh`

### Metode 2: Manual Setup

#### 1. Install Python
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip

# CentOS/RHEL
sudo yum install python3 python3-pip

# macOS (dengan Homebrew)
brew install python

# Windows: Download dari https://python.org
```

#### 2. Clone/Download Files
```bash
# Jika menggunakan git
git clone <repository-url>
cd generator-prompt-signal-binance

# Atau download dan extract ZIP file
```

#### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

#### 4. Setup Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env file dan tambahkan API key
nano .env
```

#### 5. Run Application
```bash
python generator_prompt_signal_binance.py
```

## 🔑 Konfigurasi API Key

### Gemini AI API Key

1. **Dapatkan API Key:**
   - Kunjungi: https://makersuite.google.com/app/apikey
   - Login dengan Google account
   - Klik "Create API Key"
   - Copy API key yang dihasilkan

2. **Set API Key:**

   **Metode 1: Environment Variable**
   ```bash
   # Linux/Mac
   export GEMINI_API_KEY="your_api_key_here"
   
   # Windows
   set GEMINI_API_KEY=your_api_key_here
   ```

   **Metode 2: File .env**
   ```bash
   echo "GEMINI_API_KEY=your_api_key_here" > .env
   ```

   **Metode 3: Saat Runtime**
   - Aplikasi akan meminta API key saat pertama kali dijalankan

## 🧪 Testing Installation

### Quick Test
```bash
python test_app.py
```

### Manual Test
1. Jalankan aplikasi: `python generator_prompt_signal_binance.py`
2. Buka browser: `http://localhost:5000`
3. Klik "Mulai Analisis"
4. Tunggu hasil analisis

## 🐛 Troubleshooting

### Common Issues

#### 1. Python Not Found
```
Error: 'python' is not recognized as an internal or external command
```
**Solution:**
- Install Python dari https://python.org
- Pastikan "Add to PATH" dicentang saat instalasi
- Restart terminal/command prompt

#### 2. Permission Denied (Linux/Mac)
```
Error: Permission denied
```
**Solution:**
```bash
chmod +x start.sh
chmod +x run.py
```

#### 3. Module Not Found
```
ModuleNotFoundError: No module named 'flask'
```
**Solution:**
```bash
pip install -r requirements.txt
```

#### 4. API Connection Error
```
Error: Failed to connect to Binance API
```
**Solution:**
- Cek koneksi internet
- Coba gunakan VPN jika Binance diblokir
- Tunggu beberapa menit dan coba lagi

#### 5. Gemini AI Error
```
Error: Gemini AI tidak tersedia
```
**Solution:**
- Pastikan API key sudah diset dengan benar
- Cek quota API key di Google AI Studio
- Pastikan API key masih aktif

#### 6. Port Already in Use
```
Error: Address already in use
```
**Solution:**
```bash
# Cari process yang menggunakan port 5000
lsof -i :5000  # Linux/Mac
netstat -ano | findstr :5000  # Windows

# Kill process atau ubah port di konfigurasi
```

### Performance Issues

#### 1. Analisis Lambat
**Causes:**
- Koneksi internet lambat
- Terlalu banyak pasangan dianalisis
- Resource sistem terbatas

**Solutions:**
- Kurangi MAX_PAIRS di config
- Kurangi MAX_WORKERS
- Upgrade RAM/CPU

#### 2. Memory Usage Tinggi
**Solutions:**
```python
# Edit di generator_prompt_signal_binance.py
MAX_PAIRS = 50  # Kurangi dari 100
MAX_WORKERS = 4  # Kurangi dari 8
```

## 🔧 Advanced Configuration

### Environment Variables
```bash
# Application
FLASK_ENV=development          # development/production
DEBUG=True                     # True/False
HOST=0.0.0.0                  # Bind address
PORT=5000                     # Port number

# Analysis
MAX_PAIRS=100                 # Max pairs to analyze
MAX_WORKERS=8                 # Thread workers
ANALYSIS_TIMEOUT=45           # Timeout per pair (seconds)
CACHE_TIMEOUT=300             # Cache timeout (seconds)

# Logging
LOG_LEVEL=INFO                # DEBUG/INFO/WARNING/ERROR
```

### Custom Configuration
```python
# Buat file config_custom.py
class CustomConfig:
    MAX_PAIRS = 200
    MAX_WORKERS = 12
    CONFIDENCE_THRESHOLD = 70
    
    # Custom timeframes
    TIMEFRAMES = ['4h', '1h', '30m']
    
    # Custom indicators
    RSI_PERIOD = 21
    SMA_SHORT = 10
    SMA_LONG = 30
```

## 🚀 Production Deployment

### Using Gunicorn (Recommended)
```bash
# Install gunicorn
pip install gunicorn gevent

# Run with gunicorn
gunicorn --bind 0.0.0.0:5000 --workers 4 --worker-class gevent generator_prompt_signal_binance:app
```

### Using Docker
```bash
# Build image
docker build -t binance-signal .

# Run container
docker run -d -p 5000:5000 -e GEMINI_API_KEY=your_key binance-signal
```

### Using Docker Compose
```bash
# Set environment
echo "GEMINI_API_KEY=your_key" > .env

# Start services
docker-compose up -d
```

### Systemd Service (Linux)
```bash
# Run deployment script
python deploy.py

# Follow systemd instructions
sudo cp /tmp/binance-signal.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable binance-signal
sudo systemctl start binance-signal
```

## 📊 Monitoring & Logs

### Log Files
- **Application:** `binance_analyzer.log`
- **Access:** Check console output
- **Errors:** Check application logs

### Health Check
```bash
curl http://localhost:5000/health
```

### Performance Monitoring
```python
# Check memory usage
import psutil
print(f"Memory: {psutil.virtual_memory().percent}%")
print(f"CPU: {psutil.cpu_percent()}%")
```

## 🔒 Security Considerations

### API Key Security
- ❌ Jangan commit API key ke git
- ✅ Gunakan environment variables
- ✅ Rotate API key secara berkala
- ✅ Monitor usage quota

### Network Security
- ✅ Gunakan HTTPS di production
- ✅ Setup firewall rules
- ✅ Limit access by IP jika perlu
- ✅ Regular security updates

### Application Security
- ✅ Update dependencies regularly
- ✅ Monitor for vulnerabilities
- ✅ Use reverse proxy (Nginx)
- ✅ Implement rate limiting

## 📞 Support & Help

### Getting Help
1. **Check logs:** `tail -f binance_analyzer.log`
2. **Run tests:** `python test_app.py`
3. **Check health:** `curl localhost:5000/health`
4. **Review documentation:** README.md, API_DOCS.md

### Common Commands
```bash
# Check status
systemctl status binance-signal

# View logs
journalctl -u binance-signal -f

# Restart service
systemctl restart binance-signal

# Update application
git pull
pip install -r requirements.txt
systemctl restart binance-signal
```

---

🤖 **Signal Analyzer AI by bobacheese**

*Untuk pertanyaan lebih lanjut, silakan buka issue di repository atau hubungi developer.*
