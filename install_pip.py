#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script untuk menginstall pip dan dependencies
Untuk sistem yang belum memiliki pip
"""

import sys
import os
import subprocess
import urllib.request
import tempfile
from pathlib import Path

def check_python():
    """Cek versi Python"""
    print(f"🐍 Python version: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ diperlukan!")
        return False
    print("✅ Python version OK")
    return True

def install_pip():
    """Install pip jika belum ada"""
    try:
        import pip
        print("✅ pip sudah terinstall")
        return True
    except ImportError:
        print("📦 pip belum terinstall, menginstall...")
        
        try:
            # Download get-pip.py
            print("📥 Downloading get-pip.py...")
            url = "https://bootstrap.pypa.io/get-pip.py"
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.py') as tmp_file:
                with urllib.request.urlopen(url) as response:
                    tmp_file.write(response.read())
                get_pip_path = tmp_file.name
            
            print("🔧 Installing pip...")
            result = subprocess.run([sys.executable, get_pip_path], 
                                  capture_output=True, text=True)
            
            # Cleanup
            os.unlink(get_pip_path)
            
            if result.returncode == 0:
                print("✅ pip berhasil diinstall")
                return True
            else:
                print(f"❌ Error installing pip: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error downloading/installing pip: {e}")
            return False

def install_requirements():
    """Install requirements dengan berbagai metode"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt tidak ditemukan!")
        return False
    
    # Baca requirements
    with open(requirements_file, 'r') as f:
        packages = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    print(f"📦 Installing {len(packages)} packages...")
    
    failed_packages = []
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            # Coba dengan pip module
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {package}")
            else:
                print(f"❌ {package}: {result.stderr.strip()}")
                failed_packages.append(package)
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {package}: Timeout")
            failed_packages.append(package)
        except Exception as e:
            print(f"❌ {package}: {e}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  Failed packages: {', '.join(failed_packages)}")
        print("Trying alternative installation methods...")
        
        # Coba install satu per satu dengan --user
        for package in failed_packages[:]:
            try:
                print(f"Trying {package} with --user...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", "--user", package
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"✅ {package} (user install)")
                    failed_packages.remove(package)
                    
            except Exception as e:
                print(f"❌ {package}: {e}")
    
    success_count = len(packages) - len(failed_packages)
    print(f"\n📊 Installation summary: {success_count}/{len(packages)} packages installed")
    
    return len(failed_packages) == 0

def create_minimal_requirements():
    """Buat requirements minimal jika install gagal"""
    print("🔧 Creating minimal requirements...")
    
    minimal_packages = [
        "flask==2.3.3",
        "requests==2.31.0", 
        "pandas==2.1.1",
        "numpy==1.24.3"
    ]
    
    with open("requirements_minimal.txt", "w") as f:
        for package in minimal_packages:
            f.write(package + "\n")
    
    print("✅ Created requirements_minimal.txt")
    return True

def test_imports():
    """Test import packages penting"""
    print("🧪 Testing package imports...")
    
    test_packages = [
        ("flask", "Flask web framework"),
        ("requests", "HTTP requests"),
        ("pandas", "Data analysis"),
        ("numpy", "Numerical computing")
    ]
    
    success = True
    
    for package, description in test_packages:
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} - NOT AVAILABLE")
            success = False
    
    return success

def create_simple_app():
    """Buat versi sederhana aplikasi jika dependencies tidak lengkap"""
    print("🔧 Creating simplified version...")
    
    simple_app_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generator Prompt Signal Binance - Simplified Version
Versi sederhana tanpa dependencies kompleks
"""

import sys
import json
import time
from datetime import datetime

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    print("⚠️  requests tidak tersedia - mode offline")

def get_binance_data():
    """Ambil data dari Binance API"""
    if not HAS_REQUESTS:
        return {"error": "requests module not available"}
    
    try:
        url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def analyze_simple():
    """Analisis sederhana"""
    print("🔍 Menganalisis data Binance...")
    
    data = get_binance_data()
    if "error" in data:
        print(f"❌ Error: {data['error']}")
        return []
    
    # Filter dan sort berdasarkan volume
    if isinstance(data, list):
        usdt_pairs = [item for item in data if item['symbol'].endswith('USDT')]
        sorted_pairs = sorted(usdt_pairs, key=lambda x: float(x['volume']), reverse=True)
        
        results = []
        for pair in sorted_pairs[:10]:
            price_change = float(pair['priceChangePercent'])
            signal = "BUY" if price_change > 2 else "SELL" if price_change < -2 else "NEUTRAL"
            
            results.append({
                'symbol': pair['symbol'],
                'price': pair['lastPrice'],
                'change': price_change,
                'volume': pair['volume'],
                'signal': signal
            })
        
        return results
    
    return []

def main():
    """Main function"""
    print("🚀 Generator Prompt Signal Binance - Simple Version")
    print("=" * 60)
    
    if not HAS_REQUESTS:
        print("⚠️  Mode terbatas - install 'requests' untuk fitur lengkap")
        print("Jalankan: python3 -m pip install requests")
        return
    
    while True:
        print("\\n1. Analisis Sinyal")
        print("2. Keluar")
        
        choice = input("\\nPilih opsi (1-2): ").strip()
        
        if choice == "1":
            results = analyze_simple()
            
            if results:
                print("\\n🎯 Top 10 Signals:")
                print("-" * 60)
                for i, result in enumerate(results, 1):
                    print(f"{i:2d}. {result['symbol']:12} | "
                          f"${result['price']:>10} | "
                          f"{result['change']:>6.2f}% | "
                          f"{result['signal']}")
                print("-" * 60)
            else:
                print("❌ Tidak ada data ditemukan")
                
        elif choice == "2":
            print("👋 Terima kasih!")
            break
        else:
            print("❌ Pilihan tidak valid")

if __name__ == "__main__":
    main()
'''
    
    with open("simple_app.py", "w") as f:
        f.write(simple_app_content)
    
    print("✅ Created simple_app.py")
    return True

def main():
    """Main installation function"""
    print("🚀 GENERATOR PROMPT SIGNAL BINANCE - INSTALLER")
    print("=" * 60)
    
    # Check Python
    if not check_python():
        return False
    
    # Install pip
    if not install_pip():
        print("❌ Tidak bisa menginstall pip")
        print("Silakan install pip secara manual atau gunakan package manager sistem")
        return False
    
    # Install requirements
    success = install_requirements()
    
    if not success:
        print("⚠️  Beberapa package gagal diinstall")
        print("Membuat versi minimal...")
        create_minimal_requirements()
        create_simple_app()
        
        print("\\n📋 Opsi yang tersedia:")
        print("1. Jalankan versi sederhana: python3 simple_app.py")
        print("2. Install manual: python3 -m pip install requests pandas numpy flask")
        print("3. Coba install minimal: python3 -m pip install -r requirements_minimal.txt")
    
    # Test imports
    if test_imports():
        print("\\n🎉 Semua package berhasil diinstall!")
        print("Jalankan aplikasi: python3 generator_prompt_signal_binance.py")
    else:
        print("\\n⚠️  Beberapa package tidak tersedia")
        print("Gunakan simple_app.py untuk versi sederhana")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\\n👋 Installation dibatalkan")
    except Exception as e:
        print(f"\\n❌ Error: {e}")
        sys.exit(1)
